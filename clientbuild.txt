src/components/results/justiming-mapper/tests/justiming-mapper-conformity.test.ts:171:21 - error TS18048: 'event.egOptions.seed.qualifyToEg.rules' is possibly 'undefined'.

171       expect(typeof event.egOptions.seed.qualifyToEg.rules.auto).toBe('number')
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/components/results/justiming-mapper/tests/justiming-mapper-conformity.test.ts:172:21 - error TS18048: 'event.egOptions.seed.qualifyToEg.rules' is possibly 'undefined'.

172       expect(typeof event.egOptions.seed.qualifyToEg.rules.nonAuto).toBe('number')
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/components/results/justiming-mapper/tests/justiming-mapper-edge-cases.test.ts:333:41 - error TS2554: Expected 1 arguments, but got 2.

333         expect(event.typeNo).toBe('F1', `${eventName} should be identified as field event`)
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/components/results/justiming-mapper/tests/justiming-mapper-edge-cases.test.ts:384:41 - error TS2554: Expected 1 arguments, but got 2.

384         expect(event.typeNo).toBe('T1', `${eventName} should be identified as track event`)
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/components/results/justiming-mapper/tests/justiming-mapper.service.test.ts:169:25 - error TS2339: Property 'category' does not exist on type 'IndividualEntry'.

169       expect(athlete167.category).toBe('Sen')
                            ~~~~~~~~

src/components/results/justiming-mapper/tests/justiming-mapper-conformity.test.ts:171:21 - error TS18048: 'event.egOptions.seed.qualifyToEg.rules' is possibly 'undefined'.

171       expect(typeof event.egOptions.seed.qualifyToEg.rules.auto).toBe('number')
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/components/results/justiming-mapper/tests/justiming-mapper-conformity.test.ts:172:21 - error TS18048: 'event.egOptions.seed.qualifyToEg.rules' is possibly 'undefined'.

172       expect(typeof event.egOptions.seed.qualifyToEg.rules.nonAuto).toBe('number')
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/components/results/justiming-mapper/tests/justiming-mapper-edge-cases.test.ts:333:41 - error TS2554: Expected 1 arguments, but got 2.

333         expect(event.typeNo).toBe('F1', `${eventName} should be identified as field event`)
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/components/results/justiming-mapper/tests/justiming-mapper-edge-cases.test.ts:384:41 - error TS2554: Expected 1 arguments, but got 2.

384         expect(event.typeNo).toBe('T1', `${eventName} should be identified as track event`)
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/components/results/justiming-mapper/tests/justiming-mapper.service.test.ts:169:25 - error TS2339: Property 'category' does not exist on type 'IndividualEntry'.

169       expect(athlete167.category).toBe('Sen')
                            ~~~~~~~~


Found 10 errors.