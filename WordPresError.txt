{"address":"**************","cause":{"address":"**************","code":"ETIMEDOUT","errno":-4039,"port":443,"syscall":"connect"},"code"
:"ETIMEDOUT","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"data":"log=e4sadmin&pwd=E4SEntrySystem2024%24&interim-login=1","env":{},"headers":{"Accept":"application/json, text/plain, */*","A
ccept-Encoding":"gzip, compress, deflate, br","Content-Length":"54","Content-Type":"application/x-www-form-urlencoded","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"post","timeou
t":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://dev25.entry4sports.co.uk/wp-login.php","
withCredentials":true,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"errno":-4039,"name":"Error","port":443,"request":{"_currentRequest":{"_closed":false,"_contentLength":"54","_defaultKeepAlive"
:true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /wp-login.php HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/x-www-form-urlencoded\r\nUser-Ag
ent: axios/1.10.0\r\nContent-Length: 54\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: dev25.entry4sports.co.uk\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":false,
"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaul
tPort":443,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"sch
eduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{"dev25.entry4sports.co.uk:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_contro
lReleased":true,"_events":{"close":[null,null,null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":11,"_hadError":true,"_host":"dev25.entry4sports.co.uk","_httpM
essage":"[Circular]","_newSessionPending":false,"_parent":null,"_pendingData":[{"chunk":"POST /wp-login.php HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/x-www-form-urlencoded
\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 54\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: dev25.entry4sports.co.uk\r\nConnection: keep-alive\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[108,1
11,103,61,101,52,115,97,100,109,105,110,38,112,119,100,61,69,52,83,69,110,116,114,121,83,121,115,116,101,109,50,48,50,52,37,50,52,38,105,110,116,101,114,105,109,45,108,111,103,105,110,61,49],"type":"Buffer"},"enc
oding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureE
stablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":
{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":325,"pendingcb":1,"writelen":325},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"connecting":false,"encrypted"
:true,"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":30000}]},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":false,"finished":false,"host":"dev25.entry4sports.co.uk","maxH
eadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/wp-login.php","protocol":"https:","res":null,"reusedSocket":false,"sendDate":false,"s
houldKeepAlive":true,"socket":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[nu
ll,null,null]},"_eventsCount":11,"_hadError":true,"_host":"dev25.entry4sports.co.uk","_httpMessage":"[Circular]","_newSessionPending":false,"_parent":null,"_pendingData":[{"chunk":"POST /wp-login.php HTTP/1.1\r\n
Accept: application/json, text/plain, */*\r\nContent-Type: application/x-www-form-urlencoded\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 54\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: dev25.entry4
sports.co.uk\r\nConnection: keep-alive\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[108,111,103,61,101,52,115,97,100,109,105,110,38,112,119,100,61,69,52,83,69,110,116,114,121,83,121,115,116,101,109,50,48,50,52
,37,50,52,38,105,110,116,101,114,105,109,45,108,111,103,105,110,61,49],"type":"Buffer"},"encoding":"buffer"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWate
rMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":false,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"r
ejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":325,"pendingcb":1,"writelen":325},"allowHalfOpen":false,"al
pnProtocol":null,"authorizationError":null,"authorized":false,"connecting":false,"encrypted":true,"parser":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":30000},"strictContentLength":false,"u
pgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable":true},"_currentUrl":"https://dev25.entry4sports.co.uk/wp-login.php","_ended":false,"_ending":true,"_events":{"socket":[null,null]},"_eventsCoun
t":3,"_options":{"agents":{},"beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Length":"54","Content-Type":"application/x-www-fo
rm-urlencoded","User-Agent":"axios/1.10.0"},"hostname":"dev25.entry4sports.co.uk","maxBodyLength":null,"maxRedirects":21,"method":"POST","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","C
OPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REBIND","REPORT","SEARCH","SOURCE","SUB
SCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non
-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":
"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"No
t Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Pa
yload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"L
ocked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","5
00":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient S
torage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAli
ve":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests
":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":[],"map":{}},"defaultPort":443,"freeSockets":{},"k
eepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000
},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{"dev25.entry4sports.co.uk:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"cl
ose":[null,null,null,null,null],"connect":[null,null,null],"end":[null,null],"timeout":[null,null,null]},"_eventsCount":11,"_hadError":true,"_host":"dev25.entry4sports.co.uk","_httpMessage":{"_closed":false,"_con
tentLength":"54","_defaultKeepAlive":true,"_ended":false,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"POST /wp-login.php HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: applicat
ion/x-www-form-urlencoded\r\nUser-Agent: axios/1.10.0\r\nContent-Length: 54\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: dev25.entry4sports.co.uk\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,
"_keepAliveTimeout":0,"_last":false,"_redirectable":"[Circular]","_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":"[Circular]","chunkedEncoding":false,"d
estroyed":false,"finished":false,"host":"dev25.entry4sports.co.uk","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"POST","outputData":[],"outputSize":0,"parser":null,"path":"/wp-login.php"
,"protocol":"https:","res":null,"reusedSocket":false,"sendDate":false,"shouldKeepAlive":true,"socket":"[Circular]","strictContentLength":false,"upgradeOrConnect":false,"useChunkedEncodingByDefault":true,"writable
":true},"_newSessionPending":false,"_parent":null,"_pendingData":[{"chunk":"POST /wp-login.php HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nContent-Type: application/x-www-form-urlencoded\r\nUser-Agen
t: axios/1.10.0\r\nContent-Length: 54\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: dev25.entry4sports.co.uk\r\nConnection: keep-alive\r\n\r\n","encoding":"latin1"},{"chunk":{"data":[108,111,103,61,101
,52,115,97,100,109,105,110,38,112,119,100,61,69,52,83,69,110,116,114,121,83,121,115,116,101,109,50,48,50,52,37,50,52,38,105,110,116,101,114,105,109,45,108,111,103,105,110,61,49],"type":"Buffer"},"encoding":"buffe
r"}],"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":f
alse,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedInd
ex":0,"corked":0,"highWaterMark":16384,"length":325,"pendingcb":1,"writelen":325},"allowHalfOpen":false,"alpnProtocol":null,"authorizationError":null,"authorized":false,"connecting":false,"encrypted":true,"parser
":null,"secureConnecting":true,"servername":null,"ssl":null,"timeout":30000}]},"totalSocketCount":1}}},"path":"/wp-login.php","pathname":"/wp-login.php","port":"","protocol":"https:"},"_redirectCount":0,"_redirec
ts":[],"_requestBodyBuffers":[{"data":{"data":[108,111,103,61,101,52,115,97,100,109,105,110,38,112,119,100,61,69,52,83,69,110,116,114,121,83,121,115,116,101,109,50,48,50,52,37,50,52,38,105,110,116,101,114,105,109
,45,108,111,103,105,110,61,49],"type":"Buffer"}}],"_requestBodyLength":54,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"stack":"Erro
r: connect ETIMEDOUT **************:443\n    at Function.AxiosError.from (D:\\Projects\\es4_2\\liteserver\\node_modules\\axios\\lib\\core\\AxiosError.js:92:14)\n    at RedirectableRequest.handleRequestError (D:\\
Projects\\es4_2\\liteserver\\node_modules\\axios\\lib\\adapters\\http.js:620:25)\n    at RedirectableRequest.emit (node:events:530:35)\n    at eventHandlers.<computed> (D:\\Projects\\es4_2\\liteserver\\node_modul
es\\follow-redirects\\index.js:49:24)\n    at ClientRequest.emit (node:events:518:28)\n    at TLSSocket.socketErrorListener (node:_http_client:500:9)\n    at TLSSocket.emit (node:events:518:28)\n    at emitErrorN
T (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:
\\Projects\\es4_2\\liteserver\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at WordPressAuthService.authenticate (D:\\Pro
jects\\es4_2\\liteserver\\src\\features\\wordpress\\services\\wp-auth.ts:26:24)\n    at HealthMonitor.checkWordPress (D:\\Projects\\es4_2\\liteserver\\src\\shared\\lib\\health-monitor.ts:272:7)\n    at HealthMoni
tor.performHealthCheck (D:\\Projects\\es4_2\\liteserver\\src\\shared\\lib\\health-monitor.ts:88:20)","syscall":"connect","timestamp":"2025-07-18T18:35:59.791Z"}                                                    