> liteserver@1.0.0 build
> tsc && npm run copy-static

src/features/web-scraper/__tests__/html-parser.test.ts:3:1 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

3 describe('HtmlParser', () => {
  ~~~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:6:3 - error TS2304: Cannot find name 'beforeEach'.

6   beforeEach(() => {
    ~~~~~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:10:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

10   describe('parseSchedule', () => {
     ~~~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:42:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

42     it('should parse schedule events correctly', () => {
       ~~

src/features/web-scraper/__tests__/html-parser.test.ts:45:7 - error TS2304: Cannot find name 'expect'.

45       expect(events).toHaveLength(2);
         ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:47:7 - error TS2304: Cannot find name 'expect'.

47       expect(events[0]).toEqual({
         ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:60:7 - error TS2304: Cannot find name 'expect'.

60       expect(events[1]).toEqual({
         ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:74:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

74     it('should handle invalid inputs', () => {
       ~~

src/features/web-scraper/__tests__/html-parser.test.ts:75:7 - error TS2304: Cannot find name 'expect'.

75       expect(() => parser.parseSchedule('', 'https://example.com/')).toThrow('Invalid HTML content');
         ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:76:7 - error TS2304: Cannot find name 'expect'.

76       expect(() => parser.parseSchedule(mockScheduleHtml, '')).toThrow('Invalid base URL');
         ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:79:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

79     it('should handle empty schedule', () => {
       ~~

src/features/web-scraper/__tests__/html-parser.test.ts:81:7 - error TS2304: Cannot find name 'expect'.

81       expect(() => parser.parseSchedule(emptyHtml, 'https://example.com/')).toThrow('No valid events found');
         ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:85:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

85   describe('parseHeatResults', () => {
     ~~~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:120:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

120     it('should parse heat results correctly', () => {
        ~~

src/features/web-scraper/__tests__/html-parser.test.ts:127:7 - error TS2304: Cannot find name 'expect'.

127       expect(result.eventName).toBe('100m Freestyle Men');
          ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:128:7 - error TS2304: Cannot find name 'expect'.

128       expect(result.heatNumber).toBe(1);
          ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:129:7 - error TS2304: Cannot find name 'expect'.

129       expect(result.eventLink).toBe('https://example.com/event001h01.html');
          ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:130:7 - error TS2304: Cannot find name 'expect'.

130       expect(result.nextHeatLink).toBe('https://example.com/event001h02.html');
          ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:131:7 - error TS2304: Cannot find name 'expect'.

131       expect(result.athletes).toHaveLength(2);
          ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:133:7 - error TS2304: Cannot find name 'expect'.

133       expect(result.athletes[0]).toEqual({
          ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:146:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

146     it('should handle missing next heat link', () => {
        ~~

src/features/web-scraper/__tests__/html-parser.test.ts:155:7 - error TS2304: Cannot find name 'expect'.

155       expect(result.nextHeatLink).toBeUndefined();
          ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:158:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

158     it('should extract heat number from URL', () => {
        ~~

src/features/web-scraper/__tests__/html-parser.test.ts:165:7 - error TS2304: Cannot find name 'expect'.

165       expect(result.heatNumber).toBe(5);
          ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:169:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

169   describe('URL resolution', () => {
      ~~~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:170:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

170     it('should resolve next heat URLs correctly', () => {
        ~~

src/features/web-scraper/__tests__/html-parser.test.ts:172:9 - error TS2304: Cannot find name 'mockHeatHtml'.

172         mockHeatHtml,
            ~~~~~~~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:177:7 - error TS2304: Cannot find name 'expect'.

177       expect(result.nextHeatLink).toBe('https://example.com/path/event001h02.html');
          ~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:180:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

180     it('should handle absolute URLs in next heat link', () => {
        ~~

src/features/web-scraper/__tests__/html-parser.test.ts:181:35 - error TS2304: Cannot find name 'mockHeatHtml'.

181       const htmlWithAbsoluteUrl = mockHeatHtml.replace(
                                      ~~~~~~~~~~~~

src/features/web-scraper/__tests__/html-parser.test.ts:192:7 - error TS2304: Cannot find name 'expect'.

192       expect(result.nextHeatLink).toBe('https://other.com/event001h02.html');
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:3:1 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

3 describe('ProgressManager', () => {
  ~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:6:3 - error TS2304: Cannot find name 'beforeEach'.

6   beforeEach(() => {
    ~~~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:10:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

10   describe('Session management', () => {
     ~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:11:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

11     it('should create a new session', () => {
       ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:14:7 - error TS2304: Cannot find name 'expect'.

14       expect(progress.sessionId).toBe('test-session');
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:15:7 - error TS2304: Cannot find name 'expect'.

15       expect(progress.status).toBe('idle');
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:16:7 - error TS2304: Cannot find name 'expect'.

16       expect(progress.totalEvents).toBe(0);
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:17:7 - error TS2304: Cannot find name 'expect'.

17       expect(progress.processedEvents).toBe(0);
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:18:7 - error TS2304: Cannot find name 'expect'.

18       expect(progress.errors).toEqual([]);
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:21:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

21     it('should start a session', () => {
       ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:26:7 - error TS2304: Cannot find name 'expect'.

26       expect(progress?.status).toBe('running');
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:27:7 - error TS2304: Cannot find name 'expect'.

27       expect(progress?.totalEvents).toBe(5);
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:28:7 - error TS2304: Cannot find name 'expect'.

28       expect(progress?.startTime).toBeDefined();
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:31:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

31     it('should complete a session successfully', () => {
       ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:37:7 - error TS2304: Cannot find name 'expect'.

37       expect(progress?.status).toBe('completed');
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:38:7 - error TS2304: Cannot find name 'expect'.

38       expect(progress?.endTime).toBeDefined();
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:41:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

41     it('should complete a session with error', () => {
       ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:47:7 - error TS2304: Cannot find name 'expect'.

47       expect(progress?.status).toBe('error');
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:51:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

51   describe('Progress tracking', () => {
     ~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:52:5 - error TS2304: Cannot find name 'beforeEach'.

52     beforeEach(() => {
       ~~~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:57:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

57     it('should update event progress', () => {
       ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:62:7 - error TS2304: Cannot find name 'expect'.

62       expect(progress?.currentEvent).toBe('Event 1');
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:63:7 - error TS2304: Cannot find name 'expect'.

63       expect(progress?.processedEvents).toBe(1);
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:64:7 - error TS2304: Cannot find name 'expect'.

64       expect(progress?.averageEventTime).toBeGreaterThan(0);
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:65:7 - error TS2304: Cannot find name 'expect'.

65       expect(progress?.estimatedTimeRemaining).toBeGreaterThan(0);
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:68:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

68     it('should update heat progress', () => {
       ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:72:7 - error TS2304: Cannot find name 'expect'.

72       expect(progress?.currentHeat).toBe(2);
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:73:7 - error TS2304: Cannot find name 'expect'.

73       expect(progress?.processedHeats).toBe(1);
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:76:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

76     it('should calculate estimated time remaining', () => {
       ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:83:7 - error TS2304: Cannot find name 'expect'.

83       expect(progress?.averageEventTime).toBe(1500); // Average of 2000ms and 1000ms
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:84:7 - error TS2304: Cannot find name 'expect'.

84       expect(progress?.estimatedTimeRemaining).toBe(1500); // 1 remaining event * 1500ms average
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:88:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

88   describe('Error and warning handling', () => {
     ~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:89:5 - error TS2304: Cannot find name 'beforeEach'.

89     beforeEach(() => {
       ~~~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:93:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

93     it('should add errors', () => {
       ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:98:7 - error TS2304: Cannot find name 'expect'.

98       expect(progress?.errors).toHaveLength(1);
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:99:7 - error TS2304: Cannot find name 'expect'.

99       expect(progress?.errors[0].type).toBe('network');
         ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:100:7 - error TS2304: Cannot find name 'expect'.

100       expect(progress?.errors[0].message).toBe('Connection failed');
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:101:7 - error TS2304: Cannot find name 'expect'.

101       expect(progress?.errors[0].retryable).toBe(true);
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:104:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

104     it('should add warnings', () => {
        ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:108:7 - error TS2304: Cannot find name 'expect'.

108       expect(progress?.warnings).toHaveLength(1);
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:109:7 - error TS2304: Cannot find name 'expect'.

109       expect(progress?.warnings[0]).toBe('Data quality issue');
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:112:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

112     it('should create error objects with correct structure', () => {
        ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:115:7 - error TS2304: Cannot find name 'expect'.

115       expect(error.type).toBe('parsing');
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:116:7 - error TS2304: Cannot find name 'expect'.

116       expect(error.message).toBe('Invalid HTML');
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:117:7 - error TS2304: Cannot find name 'expect'.

117       expect(error.context).toBe('event.html');
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:118:7 - error TS2304: Cannot find name 'expect'.

118       expect(error.retryable).toBe(false);
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:119:7 - error TS2304: Cannot find name 'expect'.

119       expect(error.timestamp).toBeInstanceOf(Date);
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:123:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

123   describe('Data quality tracking', () => {
      ~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:124:5 - error TS2304: Cannot find name 'beforeEach'.

124     beforeEach(() => {
        ~~~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:128:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

128     it('should update data quality metrics', () => {
        ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:136:7 - error TS2304: Cannot find name 'expect'.

136       expect(progress?.dataQuality.totalAthletes).toBe(50);
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:137:7 - error TS2304: Cannot find name 'expect'.

137       expect(progress?.dataQuality.athletesWithResults).toBe(45);
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:138:7 - error TS2304: Cannot find name 'expect'.

138       expect(progress?.dataQuality.athletesWithoutResults).toBe(5);
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:141:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

141     it('should calculate average athletes per heat on completion', () => {
        ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:152:7 - error TS2304: Cannot find name 'expect'.

152       expect(progress?.dataQuality.averageAthletesPerHeat).toBe(20); // 100 athletes / 5 heats
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:156:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

156   describe('Multiple sessions', () => {
      ~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:157:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

157     it('should handle multiple concurrent sessions', () => {
        ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:165:7 - error TS2304: Cannot find name 'expect'.

165       expect(allSessions).toHaveLength(2);
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:170:7 - error TS2304: Cannot find name 'expect'.

170       expect(session1?.totalEvents).toBe(3);
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:171:7 - error TS2304: Cannot find name 'expect'.

171       expect(session2?.totalEvents).toBe(5);
          ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:175:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

175   describe('Event emission', () => {
      ~~~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:176:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

176     it('should emit progress events', (done) => {
        ~~

src/features/web-scraper/__tests__/progress-manager.test.ts:176:40 - error TS7006: Parameter 'done' implicitly has an 'any' type.


176     it('should emit progress events', (done) => {
                                           ~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:180:9 - error TS2304: Cannot find name 'expect'.

180         expect(data.sessionId).toBe('test-session');
            ~~~~~~

src/features/web-scraper/__tests__/progress-manager.test.ts:181:9 - error TS2304: Cannot find name 'expect'.

181         expect(data.progress).toBeDefined();
            ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:5:1 - error TS2708: Cannot use namespace 'jest' as a value.

5 jest.mock('axios');
  ~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:6:35 - error TS2694: Namespace 'global.jest' has no exported member 'Mocked'.

6 const mockedAxios = axios as jest.Mocked<typeof axios>;
                                    ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:8:1 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

8 describe('WebFetcher', () => {
  ~~~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:11:3 - error TS2304: Cannot find name 'beforeEach'.

11   beforeEach(() => {
     ~~~~~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:12:39 - error TS2554: Expected 0-1 arguments, but got 3.

12     webFetcher = new WebFetcher(5000, 2, 100); // Short timeout and retry for tests
                                         ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:13:5 - error TS2708: Cannot use namespace 'jest' as a value.

13     jest.clearAllMocks();
       ~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:16:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

16   describe('URL validation', () => {
     ~~~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:17:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

17     it('should reject invalid URLs', async () => {
       ~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:18:13 - error TS2304: Cannot find name 'expect'.

18       await expect(webFetcher.fetchHtml('not-a-url')).rejects.toThrow('Invalid URL provided');
               ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:19:13 - error TS2304: Cannot find name 'expect'.

19       await expect(webFetcher.fetchHtml('ftp://example.com')).rejects.toThrow('Invalid URL provided');
               ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:22:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

22     it('should accept valid HTTP/HTTPS URLs', async () => {
       ~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:29:7 - error TS2304: Cannot find name 'expect'.

29       expect(result).toBe('<html><body>Test content</body></html>');
         ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:33:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

33   describe('Content validation', () => {
     ~~~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:34:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

34     it('should reject empty content', async () => {
       ~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:40:13 - error TS2304: Cannot find name 'expect'.

40       await expect(webFetcher.fetchHtml('https://example.com')).rejects.toThrow('empty or invalid HTML content');
               ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:43:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

43     it('should warn about short content', async () => {
       ~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:44:26 - error TS2708: Cannot use namespace 'jest' as a value.

44       const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
                            ~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:52:7 - error TS2304: Cannot find name 'expect'.

52       expect(result).toBe('<html>short</html>');
         ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:57:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

57     it('should detect error pages', async () => {
       ~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:63:13 - error TS2304: Cannot find name 'expect'.

63       await expect(webFetcher.fetchHtml('https://example.com')).rejects.toThrow('error page instead of valid content');
               ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:67:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

67   describe('Retry logic', () => {
     ~~~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:68:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

68     it('should retry on network errors', async () => {
       ~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:77:7 - error TS2304: Cannot find name 'expect'.

77       expect(result).toBe('<html><body>Success after retry</body></html>');
         ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:78:7 - error TS2304: Cannot find name 'expect'.

78       expect(mockedAxios.get).toHaveBeenCalledTimes(2);
         ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:81:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

81     it('should fail after max retries', async () => {
       ~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:84:13 - error TS2304: Cannot find name 'expect'.

84       await expect(webFetcher.fetchHtml('https://example.com')).rejects.toThrow('Failed to fetch');
               ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:85:7 - error TS2304: Cannot find name 'expect'.

85       expect(mockedAxios.get).toHaveBeenCalledTimes(2); // Initial + 1 retry
         ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:88:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

88     it('should handle different error types', async () => {
       ~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:94:13 - error TS2304: Cannot find name 'expect'.

94       await expect(webFetcher.fetchHtml('https://example.com')).rejects.toThrow('Connection refused');
               ~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:98:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

98   describe('HTTP status handling', () => {
     ~~~~~~~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:99:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

99     it('should handle non-200 status codes', async () => {
       ~~

src/features/web-scraper/__tests__/web-fetcher.test.ts:106:13 - error TS2304: Cannot find name 'expect'.

106       await expect(webFetcher.fetchHtml('https://example.com')).rejects.toThrow('HTTP 404: Not Found');
                ~~~~~~


Found 128 errors in 3 files.

Errors  Files
    31  src/features/web-scraper/__tests__/html-parser.test.ts:3
    65  src/features/web-scraper/__tests__/progress-manager.test.ts:3
    32  src/features/web-scraper/__tests__/web-fetcher.test.ts:5